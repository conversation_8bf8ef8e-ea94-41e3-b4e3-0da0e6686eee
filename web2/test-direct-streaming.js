/**
 * 直接测试模型流式输出
 */

const { createModel } = require('./src/graph/designToCode/model/index.ts');
const { HumanMessage } = require('@langchain/core/messages');

async function testDirectModelStreaming() {
  console.log('开始测试模型直接流式输出...');
  
  try {
    const model = createModel();
    console.log('模型创建成功');

    const messages = [new HumanMessage("请写一首关于春天的诗，要求至少4行")];
    
    console.log('\n=== 测试 model.stream() ===');
    const startTime = Date.now();
    
    const stream = await model.stream(messages);
    let chunkCount = 0;
    let fullContent = '';
    
    for await (const chunk of stream) {
      chunkCount++;
      const currentTime = Date.now();
      const content = chunk.content || '';
      fullContent += content;
      
      console.log(`块 ${chunkCount} (${currentTime - startTime}ms): "${content}"`);
      
      if (chunkCount > 20) {
        console.log('... (限制输出)');
        break;
      }
    }
    
    console.log(`\n总块数: ${chunkCount}`);
    console.log(`总内容: "${fullContent}"`);
    console.log(`总时间: ${Date.now() - startTime}ms`);

    console.log('\n=== 测试 model.invoke() ===');
    const invokeStartTime = Date.now();
    const response = await model.invoke(messages);
    console.log(`invoke 响应时间: ${Date.now() - invokeStartTime}ms`);
    console.log(`invoke 内容: "${response.content}"`);
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
testDirectModelStreaming();
