/**
 * 测试流式输出的详细脚本
 */

async function testStreamingDetails() {
  console.log('开始详细测试流式输出...');
  
  try {
    const startTime = Date.now();
    
    const response = await fetch('http://localhost:3001/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: '请写一首关于春天的诗，要求至少4行'
          }
        ]
      })
    });

    console.log('响应状态:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API 错误:', errorText);
      return;
    }

    // 检查流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let chunkCount = 0;
    let totalContent = '';
    let firstChunkTime = null;
    let lastChunkTime = null;
    
    console.log('\n=== 流式响应详情 ===');
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const currentTime = Date.now();
      if (firstChunkTime === null) {
        firstChunkTime = currentTime;
        console.log(`首个块到达时间: ${currentTime - startTime}ms`);
      }
      lastChunkTime = currentTime;
      
      chunkCount++;
      const chunk = decoder.decode(value);
      totalContent += chunk;
      
      // 显示前几个块的详细信息
      if (chunkCount <= 10) {
        console.log(`块 ${chunkCount} (${currentTime - startTime}ms):`, 
          chunk.substring(0, 50).replace(/\n/g, '\\n') + 
          (chunk.length > 50 ? '...' : ''));
      } else if (chunkCount === 11) {
        console.log('... (后续块省略)');
      }
    }

    const endTime = Date.now();
    
    console.log('\n=== 流式统计 ===');
    console.log(`总块数: ${chunkCount}`);
    console.log(`总时间: ${endTime - startTime}ms`);
    console.log(`首块延迟: ${firstChunkTime - startTime}ms`);
    console.log(`最后块时间: ${lastChunkTime - startTime}ms`);
    console.log(`总内容长度: ${totalContent.length} 字符`);
    
    if (chunkCount > 1) {
      console.log(`平均块间隔: ${(lastChunkTime - firstChunkTime) / (chunkCount - 1)}ms`);
    }
    
    // 分析内容
    console.log('\n=== 内容分析 ===');
    const lines = totalContent.split('\n').filter(line => line.trim());
    console.log(`内容行数: ${lines.length}`);
    console.log('前几行内容:');
    lines.slice(0, 5).forEach((line, i) => {
      console.log(`  ${i + 1}: ${line.substring(0, 80)}${line.length > 80 ? '...' : ''}`);
    });

    console.log('\n流式测试完成！');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
testStreamingDetails();
