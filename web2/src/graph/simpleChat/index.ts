import { StateGraph, END, START } from "@langchain/langgraph";
import { BaseMessage, SystemMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { createModel } from "../designToCode/model/index";

// 简化的聊天状态
interface SimpleChatState {
  messages: BaseMessage[];
  input: string;
  output: string;
}

// 简化的状态对象定义
const SimpleChatStateObj = {
  messages: {
    value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  },
  input: {
    value: (x: string, y: string) => y,
    default: () => "",
  },
  output: {
    value: (x: string, y: string) => y,
    default: () => "",
  },
};

/**
 * 简单的聊天节点 - 直接回复用户消息
 */
async function simpleChatNode(state: SimpleChatState) {
  console.log("SimpleChat: 处理用户消息...");

  // 获取用户输入
  const userInput = state.input || "你好";

  // 创建模型
  const model = createModel();

  // 构建消息
  const systemMessage = new SystemMessage("你是一个友好的AI助手。请简洁地回复用户的问题。");
  const humanMessage = new HumanMessage(userInput);

  const messages = [systemMessage, humanMessage];

  console.log(`SimpleChat: 调用模型处理消息: ${userInput}`);

  try {
    // 调用模型 - 使用流式输出
    const stream = await model.stream(messages);
    let fullResponse = "";

    for await (const chunk of stream) {
      if (chunk.content) {
        fullResponse += chunk.content;
      }
    }

    console.log("SimpleChat: 模型响应成功");

    const response = new AIMessage(fullResponse);

    return {
      messages: [humanMessage, response],
      output: fullResponse,
    };
  } catch (error) {
    console.error("SimpleChat: 模型调用失败:", error);

    const errorResponse = new AIMessage("抱歉，我遇到了一些问题，请稍后再试。");

    return {
      messages: [humanMessage, errorResponse],
      output: errorResponse.content as string,
    };
  }
}

// 构建简单聊天工作流图
const workflow = new StateGraph<SimpleChatState>({
  channels: SimpleChatStateObj,
})
  .addNode("chat", simpleChatNode)
  .addEdge(START, "chat")
  .addEdge("chat", END);

// 编译工作流 - 不使用checkpointer
export const graph = workflow.compile();
graph.name = "simpleChat";

console.log("SimpleChat: 图已创建并编译完成");
