import { StateGraph, END, START, Annotation } from "@langchain/langgraph";
import { SystemMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import type { BaseMessageLike } from "@langchain/core/messages";
import { createModel } from "../designToCode/model/index";

// 使用 LangGraph 推荐的 Annotation 方式定义状态
const SimpleChatStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessageLike[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  input: Annotation<string>({
    reducer: (x, y) => y || x,
    default: () => "",
  }),
  output: Annotation<string>({
    reducer: (x, y) => y || x,
    default: () => "",
  }),
});

/**
 * 简单的聊天节点 - 使用 LangGraph 推荐的流式处理方式
 * 根据官方文档，对于 @langchain/core >= 0.2.3，直接使用 model.invoke() 即可自动支持流式
 */
async function simpleChatNode(state: typeof SimpleChatStateAnnotation.State) {
  console.log("SimpleChat: 处理用户消息...", {
    messagesCount: state.messages.length,
    input: state.input
  });

  // 创建模型
  const model = createModel();

  // 构建消息列表 - 使用现有的消息历史
  let messages = [...state.messages];

  // 如果没有消息历史，添加系统消息
  if (messages.length === 0) {
    messages.push(new SystemMessage("你是一个友好的AI助手。请简洁地回复用户的问题。"));
  }

  // 如果有额外的输入，添加为用户消息
  if (state.input && state.input.trim()) {
    messages.push(new HumanMessage(state.input));
  }

  console.log(`SimpleChat: 调用模型处理 ${messages.length} 条消息`);

  try {
    // 根据 LangGraph 官方文档，要实现真正的流式输出，
    // 需要直接使用 model.invoke() 并让 LangGraph 处理流式传输
    // 关键是确保模型支持流式，并且 LangGraph 会自动处理流式事件
    const responseMessage = await model.invoke(messages);

    console.log("SimpleChat: 模型响应成功", {
      responseLength: responseMessage.content?.toString().length || 0
    });

    return {
      messages: [responseMessage], // 返回完整的响应消息
      output: responseMessage.content?.toString() || "",
    };
  } catch (error) {
    console.error("SimpleChat: 模型调用失败:", error);

    const errorResponse = new AIMessage("抱歉，我遇到了一些问题，请稍后再试。");

    return {
      messages: [errorResponse],
      output: errorResponse.content as string,
    };
  }
}

// 构建简单聊天工作流图 - 使用 LangGraph 推荐的 Annotation 方式
const workflow = new StateGraph(SimpleChatStateAnnotation)
  .addNode("chat", simpleChatNode)
  .addEdge(START, "chat")
  .addEdge("chat", END);

// 编译工作流 - 不使用checkpointer
export const graph = workflow.compile();
graph.name = "simpleChat";

console.log("SimpleChat: 图已创建并编译完成");
