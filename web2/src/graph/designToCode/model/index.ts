import { createGoogleModel } from "./google";
import { createOpenaiModel } from "./openai";

/**
 * 创建模型 - 确保支持流式输出
 * @param type 模型类型
 * @returns 模型
 */
export function createModel(type?: "google" | "openai") {
  switch (type) {
    case "google":
      return createGoogleModel();
    case "openai":
      return createOpenaiModel();
    default:
      // 默认使用 OpenAI 模型，因为它对流式支持更好
      return createOpenaiModel();
  }
}
