import { NextRequest, NextResponse } from 'next/server';
import { type Message, LangChainAdapter } from 'ai';
import { designToHtmlGraph } from "../../../graph/designToCode"
import { graph } from "../../../graph/simpleChat"

import { convertVercelMessageToLangChainMessage } from '@/utils/message-converters';
import { logToolCallsInDevelopment } from '@/utils/stream-logging';

/**
 * This handler initializes and calls an tool calling ReAct agent.
 * See the docs for more information:
 *
 * https://langchain-ai.github.io/langgraphjs/tutorials/quickstart/
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    console.log('Chat API: 收到请求', {
      messagesCount: body.messages?.length || 0,
      hasMessages: !!body.messages
    });

    /**
     * We represent intermediate steps as system messages for display purposes,
     * but don't want them in the chat history.
     */
    const messages = (body.messages ?? [])
      .filter((message: Message) => message.role === 'user' || message.role === 'assistant')
      .map(convertVercelMessageToLangChainMessage);

    console.log('Chat API: 处理后的消息', {
      messagesCount: messages.length,
      lastMessage: messages[messages.length - 1]?.content
    });

    // 获取最后一条用户消息作为输入
    const lastUserMessage = messages
      .filter((msg: any) => msg.getType() === 'human')
      .pop();

    const userInput = (lastUserMessage?.content as string) || '';

    /**
     * Stream back all generated tokens and steps from their runs.
     *
     * 根据 LangGraph 官方文档，使用 streamEvents 来获得流式输出
     * See: https://langchain-ai.github.io/langgraphjs/how-tos/stream-tokens/
     */
    const graphInput = {
      messages: messages,
      input: userInput,
      output: ''
    };

    console.log('Chat API: 调用 graph', {
      input: userInput,
      messagesCount: messages.length
    });

    // 根据 LangGraph 官方文档，使用 streamEvents 来获得流式输出
    // 确保使用正确的配置来启用流式处理
    const eventStream = graph.streamEvents(graphInput, {
      version: 'v2',
      // 添加配置以确保流式处理正常工作
      configurable: {}
    });

    // Log tool calling data. Only in development mode
    const transformedStream = logToolCallsInDevelopment(eventStream);
    // Adapt the LangChain stream to Vercel AI SDK Stream
    return LangChainAdapter.toDataStreamResponse(transformedStream);
  } catch (e: any) {
    console.error('Chat API: 错误', e);
    return NextResponse.json({ error: e.message }, { status: e.status ?? 500 });
  }
}
