/**
 * 测试 Chat API 的脚本
 */

async function testChatAPI() {
  console.log('开始测试 Chat API...');
  
  try {
    const response = await fetch('http://localhost:3001/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: '你好，请介绍一下你自己'
          }
        ]
      })
    });

    console.log('响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API 错误:', errorText);
      return;
    }

    // 检查是否是流式响应
    if (response.headers.get('content-type')?.includes('text/plain')) {
      console.log('\n=== 流式响应 ===');
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      
      let chunkCount = 0;
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        chunkCount++;
        const chunk = decoder.decode(value);
        console.log(`块 ${chunkCount}:`, chunk.substring(0, 100) + (chunk.length > 100 ? '...' : ''));
        
        // 限制输出
        if (chunkCount > 10) {
          console.log('... (更多块被截断)');
          break;
        }
      }
    } else {
      const responseText = await response.text();
      console.log('响应内容:', responseText.substring(0, 500));
    }

    console.log('\nAPI 测试完成！');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
testChatAPI();
