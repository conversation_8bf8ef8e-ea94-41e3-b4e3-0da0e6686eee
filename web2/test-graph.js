/**
 * 测试 LangGraph 集成的简单脚本
 */

const { graph } = require('./src/graph/simpleChat/index.ts');

async function testGraph() {
  console.log('开始测试 LangGraph...');
  
  try {
    // 测试输入
    const testInput = {
      messages: [],
      input: "你好，请介绍一下你自己",
      output: ""
    };

    console.log('测试输入:', testInput);

    // 使用 streamEvents 测试
    console.log('\n=== 测试 streamEvents ===');
    const eventStream = graph.streamEvents(testInput, { version: 'v2' });
    
    let eventCount = 0;
    for await (const event of eventStream) {
      eventCount++;
      console.log(`事件 ${eventCount}:`, {
        event: event.event,
        name: event.name,
        data: event.data?.chunk?.content || event.data?.output || '...'
      });
      
      // 限制输出数量
      if (eventCount > 20) {
        console.log('... (更多事件被截断)');
        break;
      }
    }

    console.log('\n=== 测试 stream ===');
    const stream = await graph.stream(testInput);
    
    let stepCount = 0;
    for await (const step of stream) {
      stepCount++;
      console.log(`步骤 ${stepCount}:`, step);
    }

    console.log('\n测试完成！');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
testGraph();
